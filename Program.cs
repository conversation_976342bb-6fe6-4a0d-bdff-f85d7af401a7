using System.Collections.Generic;
using System.Diagnostics;
using System.IO.Compression;
using System.Net;
using System.Security.AccessControl;
using System.Security.Cryptography.X509Certificates;
using IniParser;
using IniParser.Model;


namespace kisEM_CM_Agent_Update
{
    internal class Program
    {
        static void Main(string[] args)
        {
            // 添加进程退出事件处理
            AppDomain.CurrentDomain.ProcessExit += (sender, e) =>
            {
                Logger.Instance.Log("进程正在退出...");
            };

            AppDomain.CurrentDomain.UnhandledException += (sender, e) =>
            {
                Logger.Instance.Log($"未处理的异常: {e.ExceptionObject}");
                Logger.Instance.Log($"异常类型: {e.ExceptionObject.GetType().FullName}");
                Logger.Instance.Log($"是否致命: {e.IsTerminating}");
                Logger.Instance.Log("程序即将异常退出");
            };

            // 获取临时文件路径
            string updateagent_basePath = AppDomain.CurrentDomain.BaseDirectory;
            string parentDir = Path.GetFullPath(Path.Combine(updateagent_basePath, ".."));
            string tempfilepath = Path.Combine(parentDir, "temp", "temp001.tmp");

            Logger.Instance.Log("更新程序启动，开始监控临时文件...");
            Logger.Instance.Log($"监控文件路径: {tempfilepath}");

            // 主循环：每10秒检查一次临时文件
            while (true)
            {
                try
                {
                    if (File.Exists(tempfilepath))
                    {
                        Logger.Instance.Log("发现更新触发文件，开始处理...");

                        // 读取文件内容
                        string fileContent = File.ReadAllText(tempfilepath).Trim();
                        Logger.Instance.Log($"文件内容: {fileContent}");

                        if (!string.IsNullOrEmpty(fileContent))
                        {
                            // 解析参数
                            string[] newArgs = fileContent.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                            Logger.Instance.Log($"解析到 {newArgs.Length} 个参数");

                            // 执行更新逻辑
                            ExecuteUpdateProcess(newArgs);
                        }

                        // 删除临时文件
                        try
                        {
                            File.Delete(tempfilepath);
                            Logger.Instance.Log("临时文件已删除");
                        }
                        catch (Exception deleteEx)
                        {
                            Logger.Instance.Log($"删除临时文件失败: {deleteEx.Message}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    Logger.Instance.Log($"检查临时文件时发生异常: {ex.Message}");
                }

                // 等待10秒后继续下一次检查
                Thread.Sleep(10000);
            }
        }

        // 将原有的更新逻辑提取为独立方法
        private static void ExecuteUpdateProcess(string[] args)
        {

            /*下载的临时文件和备份都放到upateagent_basePath下
             
            1、停止服务 → systemctl stop kisem - cmagent

            2、备份目录 → 把服务可执行文件所在目录（及子目录）完整复制到一个备份目录
            下载并解压更新包 → 覆盖原目录

            3、启动服务 → systemctl start kisem - cmagent

            A、如果启动失败：

            恢复备份

            再次启动服务

            终止，不再执行 ChangeConfigTxt 和 SendDeviceInfo

            B、如果成功：继续执行 ChangeConfigTxt 和 SendDeviceInfo
            */
            Logger.Instance.Log("###开始进行agent升级......");

            string updateagent_basePath = AppDomain.CurrentDomain.BaseDirectory;// 获取当前update程序所在目录
            string parentDir = Path.GetFullPath(Path.Combine(updateagent_basePath, ".."));
            string? service_basePath = Path.Combine(parentDir, "configMGMT"); ;// 获取服务所在目录，与update目录不同
            string zipPath = Path.Combine(updateagent_basePath, "cmagent.zip");//下载后的zip文件
            string serviceName = "kisem-cmagent"; // Linux 下 systemd 服务名
            string hostname = Environment.MachineName;
            
           string downloadUrl = args[0];
           string vip = args[1];
           string localServerName = args[2];
           string localSSLPort = args[3];
           string agentversion = args[4];
           string hostip = args[5];
           string username = args[6];

           string devicebase_Request_URL = $"https://{localServerName}:{localSSLPort}/api/ReportDevice/{hostname}";
           
/*
            string downloadUrl = "http://***********:9876/cmagent.zip";
            //string vip = "";
            string vip = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ1c2VyX2lkX2hlcmUiLCJqdGkiOiI0MWMzYTdiMC1lMGYxLTRmMjYtYjcyOC1mMzlhYzg0Y2QzNGEiLCJpYXQiOiIxNjI2NzE3NjA5IiwiZXhwIjoxNjI2NzE5NDA5fQ.Z0dRm9x5AlzchTAdKDHPcHLeQvTzKlfPbzC8ld8w8Yk";


            string localServerName = "***********";
            string localSSLPort = "6789";
            string agentversion = "2.3";
            string hostip = "*************";
            string username = "dev";

            string devicebase_Request_URL = $"https://***********:6789/api/ReportDevice/dev-pc";

*/


            try
            {

                // 1. 停止服务

                Logger.Instance.Log($"进入停止服务");
                bool stopResult = ExecuteCommand($"sudo /usr/bin/systemctl stop {serviceName}");

                if (!stopResult)
                {
                    Logger.Instance.Log("服务停止失败");
                    return;
                }

                // 2. 备份到updateagent_basePath
                Logger.Instance.Log($"进入备份");

                if (!Directory.Exists(service_basePath))
                {
                    Logger.Instance.Log($"错误: 源目录不存在: {service_basePath}");
                    return;
                }

                string backupDir = Path.Combine(updateagent_basePath, "backup_" + DateTime.Now.ToString("yyyyMMddHHmmss"));
                Logger.Instance.Log($"开始复制到: {backupDir}");


                try
                {
                    CopyDirectory(service_basePath, backupDir);
                    Logger.Instance.Log($"已备份到: {backupDir}");
                }
                catch (Exception ex)
                {
                    Logger.Instance.Log($"备份失败: {ex.Message}");
                    Logger.Instance.Log($"异常详情: {ex.StackTrace}");
                    return;
                }

                // 3. 下载更新包
                if (File.Exists(zipPath)) File.Delete(zipPath);
                using (WebClient client = new WebClient())
                {
                    client.DownloadFile(downloadUrl, zipPath);
                }
                if (!File.Exists(zipPath))
                {
                    Logger.Instance.Log("下载失败，未找到 cmagent.zip");
                    return;
                }

                // 4. 解压覆盖
                ZipFile.ExtractToDirectory(zipPath, service_basePath, overwriteFiles: true);
                Logger.Instance.Log("解压完成！");

                // 5. 启动服务
                if (!ExecuteCommand($"systemctl start {serviceName}"))
                {
                    Logger.Instance.Log("新版本服务启动失败，正在恢复备份...");

                    // 恢复备份
                    CopyDirectory(backupDir, service_basePath, overwrite: true);

                    // 尝试启动旧版本服务
                    ExecuteCommand($"systemctl start {serviceName}");

                    Logger.Instance.Log("恢复完成，更新中止。");
                    return; // 不执行 ChangeConfigTxt 和 SendDeviceInfo
                }

                // 6. 修改配置
                ChangeConfigTxt(agentversion, service_basePath);

                // 7. 注册新版本
                SendDeviceInfo(devicebase_Request_URL, DateTime.Now, hostip, hostname, username, agentversion, vip);
                // 8. 清除一月前的备份
                CleanOldBackups(updateagent_basePath);

                Logger.Instance.Log("###更新完成！");
            }
            catch (Exception ex)
            {
                Logger.Instance.Log("###发生错误: " + ex.Message);
            }
            finally
            {
                Logger.Instance.Log("===== 更新流程结束，返回监控模式 =====");
            }
        }


        // 递归复制目录
        public static void CopyDirectory(string sourceDir, string targetDir, bool overwrite = false)
        {
            try
            {
                // 检查源目录是否存在
                if (!Directory.Exists(sourceDir))
                {
                    Logger.Instance.Log($"[CopyDirectory] 错误: 源目录不存在: {sourceDir}");
                    throw new DirectoryNotFoundException($"源目录不存在: {sourceDir}");
                }

                // 先创建目标目录
                Directory.CreateDirectory(targetDir);


                // 先递归复制子目录

                string[] subDirs;
                try
                {
                    subDirs = Directory.GetDirectories(sourceDir);

                }
                catch (Exception ex)
                {
                    Logger.Instance.Log($"[CopyDirectory] 扫描子目录失败: {ex.Message}");
                    throw;
                }

                foreach (string dir in subDirs)
                {
                    try
                    {

                        string targetSubDir = Path.Combine(targetDir, Path.GetFileName(dir));
                        CopyDirectory(dir, targetSubDir, overwrite);
                    }
                    catch (Exception ex)
                    {
                        Logger.Instance.Log($"[CopyDirectory] 复制子目录失败: {dir} -> {ex.Message}");
                        throw;
                    }
                }

                // 再复制文件
                var files = Directory.GetFiles(sourceDir);


                foreach (string file in files)
                {
                    try
                    {
                        string targetFile = Path.Combine(targetDir, Path.GetFileName(file));
                        File.Copy(file, targetFile, overwrite);
                    }
                    catch (Exception fileEx)
                    {
                        Logger.Instance.Log($"[CopyDirectory] 复制文件失败: {file} -> {fileEx.Message}");
                        throw;
                    }
                }

                Logger.Instance.Log($"[CopyDirectory] 完成复制: {sourceDir}");
            }
            catch (Exception ex)
            {
                Logger.Instance.Log($"[CopyDirectory] 复制目录失败: {sourceDir} -> {ex.Message}");
                throw;
            }
        }
        public static void ChangeConfigTxt(string agentversion, string? service_basePath = null)
        {
            try
            {
                if (string.IsNullOrEmpty(service_basePath))
                {
                    Logger.Instance.Log("错误: service_basePath 参数为空");
                    return;
                }

                var parser = new FileIniDataParser();
                string localFilePath = Path.Combine(service_basePath, "settings.txt");

                if (!File.Exists(localFilePath))
                {
                    Logger.Instance.Log($"错误: 配置文件不存在: {localFilePath}");
                    return;
                }

                Logger.Instance.Log($"正在修改配置文件: {localFilePath}");
                IniData data = parser.ReadFile(localFilePath);
                data["kisCM"]["agentversion"] = agentversion;
                parser.WriteFile(localFilePath, data);
                Logger.Instance.Log($"配置文件修改成功，agentversion = {agentversion}");
            }
            catch (Exception ex)
            {
                Logger.Instance.Log($"修改配置文件失败: {ex.Message}");
            }
        }

        public static void SendDeviceInfo(string url, DateTime datetime, string ip, string pcname, string uname, string agentver, string token)
        {
            var DeviceInfo = new
            {
                registerdatetime = datetime,
                hostip = ip,
                hostname = pcname,
                username = uname,
                agentversion = agentver
            };

            var json = Newtonsoft.Json.JsonConvert.SerializeObject(DeviceInfo);

            System.Net.ServicePointManager.ServerCertificateValidationCallback +=
                (sender, cert, chain, sslPolicyErrors) => true;

            var request = (HttpWebRequest)WebRequest.Create(url);
            request.Method = "POST";
            request.ContentType = "application/json";
            request.Headers["Authorization"] = $"Bearer {token}";

            using (var streamWriter = new StreamWriter(request.GetRequestStream()))
            {
                streamWriter.Write(json);
                streamWriter.Flush();
                streamWriter.Close();
            }

            var response = (HttpWebResponse)request.GetResponse();
            using (var streamReader = new StreamReader(response.GetResponseStream()))
            {
                var result = streamReader.ReadToEnd();
                Logger.Instance.Log("服务器返回: " + result);
            }
        }

        public static void CleanOldBackups(string basePath)
        {
            try
            {
                if (!Directory.Exists(basePath)) return;

                var backupDirs = Directory.GetDirectories(basePath, "backup_*");

                foreach (var dir in backupDirs)
                {
                    // 获取目录名中的时间戳
                    string dirName = Path.GetFileName(dir);
                    string timestamp = dirName.Substring("backup_".Length);

                    if (DateTime.TryParseExact(timestamp, "yyyyMMddHHmmss", null,
                                               System.Globalization.DateTimeStyles.None,
                                               out DateTime backupTime))
                    {
                        // 如果备份超过30天，则删除
                        if ((DateTime.Now - backupTime).TotalDays > 30)
                        {
                            Directory.Delete(dir, true);
                            Logger.Instance.Log($"已删除旧备份目录: {dir}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Log($"清理旧备份失败: {ex.Message}");
            }
        }

        private static bool ExecuteCommand3(string command)

        {

            try
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "/bin/bash",
                        Arguments = $"-c \"{command}\"",
                        RedirectStandardOutput = false,
                        RedirectStandardError = false,
                        UseShellExecute = false,
                        CreateNoWindow = true
                    }
                };
                process.Start();
                // process.WaitForExit();
                return true;
            }
            catch (Exception ex)
            {
                Logger.Instance.Log($"启动更新程序失败: {ex.Message}");
                return false;
            }
        }

        private static bool ExecuteCommand2(string command)
        {
            try
            {
                var psi = new ProcessStartInfo
                {
                    FileName = "/bin/bash",
                    Arguments = $"-c \"{command}\"",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true



                };

                using (var process = Process.Start(psi))
                {
                    process.WaitForExit();
                    if (process.ExitCode != 0)
                    {
                        string error = process.StandardError.ReadToEnd();
                        Logger.Instance.Log($"命令执行失败: {command}, 错误: {error}");
                        return false;
                    }
                }

                Logger.Instance.Log($"命令执行成功: {command}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Instance.Log($"执行命令异常: {command}, 错误: {ex.Message}");
                return false;
            }
        }
        

        private static bool ExecuteCommand(string command, int timeoutMs = 30000)
{
    try
    {
        var process = new Process
        {
            StartInfo = new ProcessStartInfo
            {
                FileName = "/bin/bash",
                Arguments = $"-c \"{command}\"",
                UseShellExecute = false,
                RedirectStandardOutput = false,
                RedirectStandardError = false,
                CreateNoWindow = true
            }
        };

        Logger.Instance.Log($"[DEBUG] 开始执行命令: {command}");
        process.Start();
        
        // 使用超时等待
        if (!process.WaitForExit(timeoutMs))
        {
            Logger.Instance.Log($"命令执行超时({timeoutMs}ms)，强制终止: {command}");
            try 
            { 
                process.Kill(); 
                Logger.Instance.Log("命令进程已被终止");
            } 
            catch (Exception killEx) 
            { 
                Logger.Instance.Log($"终止命令进程失败: {killEx.Message}");
            }
            
            // 对于停止服务命令，超时也认为是成功的（因为服务实际已停止）
            if (command.Contains("systemctl stop"))
            {
                Logger.Instance.Log("停止服务命令超时，但假设服务已停止");
                return true;
            }
            
            return false;
        }

        Logger.Instance.Log($"命令执行完成: {command}, ExitCode: {process.ExitCode}");
        return process.ExitCode == 0;
    }
    catch (Exception ex)
    {
        Logger.Instance.Log($"执行命令异常: {command}, 异常: {ex.Message}");
        return false;
    }
}
    }
}
