﻿using System.Collections.Generic;
using System.Diagnostics;
using System.IO.Compression;
using System.Net;
using System.Security.AccessControl;
using System.Security.Cryptography.X509Certificates;
using IniParser;
using IniParser.Model;


namespace kisEM_CM_Agent_Update
{
    internal class Program
    {
        static void Main(string[] args)
        {
            /*下载的临时文件和备份都放到upateagent_basePath下
             
            1、停止服务 → systemctl stop kisem - cmagent

            2、备份目录 → 把服务可执行文件所在目录（及子目录）完整复制到一个备份目录
            下载并解压更新包 → 覆盖原目录

            3、启动服务 → systemctl start kisem - cmagent

            A、如果启动失败：

            恢复备份

            再次启动服务

            终止，不再执行 ChangeConfigTxt 和 SendDeviceInfo

            B、如果成功：继续执行 ChangeConfigTxt 和 SendDeviceInfo
            */
            Logger.Instance.Log("###开始进行agent升级......");
                      
            string updateagent_basePath = AppDomain.CurrentDomain.BaseDirectory;// 获取当前update程序所在目录

            string parentDir = Path.GetFullPath(Path.Combine(updateagent_basePath, ".."));

            string? service_basePath = Path.Combine(parentDir, "configMGMT"); ;// 获取服务所在目录，与update目录不同
                   

            string zipPath = Path.Combine(updateagent_basePath, "cmagent.zip");//下载后的zip文件
            string serviceName = "kisem-cmagent"; // Linux 下 systemd 服务名

            string hostname = Environment.MachineName;

            string downloadUrl = args[0];
            string vip = args[1];
            string localServerName = args[2];
            string localSSLPort = args[3];
            string agentversion = args[4];
            string hostip = args[5];
            string username = args[6];

            string devicebase_Request_URL = $"https://{localServerName}:{localSSLPort}/api/ReportDevice/{hostname}";

            try
            {
                
                // 1. 停止服务
                if (!ExecuteCommand($"systemctl stop {serviceName}"))
                {
                    Logger.Instance.Log("停止服务失败，退出。");
                    return;
                }

                // 2. 备份到updateagent_basePath
                string backupDir = Path.Combine(updateagent_basePath, "backup_" + DateTime.Now.ToString("yyyyMMddHHmmss"));
                CopyDirectory(service_basePath, backupDir);
                Logger.Instance.Log($"已备份到: {backupDir}");

                // 3. 下载更新包
                if (File.Exists(zipPath)) File.Delete(zipPath);
                using (WebClient client = new WebClient())
                {
                    client.DownloadFile(downloadUrl, zipPath);
                }
                if (!File.Exists(zipPath))
                {
                    Logger.Instance.Log("下载失败，未找到 cmagent.zip");
                    return;
                }

                // 4. 解压覆盖
                ZipFile.ExtractToDirectory(zipPath, service_basePath, overwriteFiles: true);
                Logger.Instance.Log("解压完成！");

                // 5. 启动服务
                if (!ExecuteCommand($"systemctl start {serviceName}"))
                {
                    Logger.Instance.Log("新版本服务启动失败，正在恢复备份...");

                    // 恢复备份
                    CopyDirectory(backupDir, service_basePath, overwrite: true);

                    // 尝试启动旧版本服务
                    ExecuteCommand($"systemctl start {serviceName}");

                    Logger.Instance.Log("恢复完成，更新中止。");
                    return; // 不执行 ChangeConfigTxt 和 SendDeviceInfo
                }

                // 6. 修改配置
                ChangeConfigTxt(agentversion);

                // 7. 注册新版本
                SendDeviceInfo(devicebase_Request_URL, DateTime.Now, hostip, hostname, username, agentversion, vip);
                // 8. 清除一月前的备份
                CleanOldBackups(updateagent_basePath);

                Logger.Instance.Log("###更新完成！");
            }
            catch (Exception ex)
            {
                Logger.Instance.Log("###发生错误: " + ex.Message);
            }
        }

        // 执行 systemctl 命令
       



        public static bool ExecuteCommand(string command)
        {
            try
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "/bin/bash",
                        Arguments = $"-c \"{command}\"",
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        CreateNoWindow = true
                    }
                };

                process.Start();
                process.WaitForExit();

                if (process.ExitCode != 0)
                {
                    string error = process.StandardError.ReadToEnd();
                    Logger.Instance.Log($"命令执行失败: {command}, 错误: {error}");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                Logger.Instance.Log($"执行命令异常: {command}, 异常: {ex.Message}");
                return false;
            }
        }

        // 递归复制目录
        public static void CopyDirectory(string sourceDir, string targetDir, bool overwrite = false)
        {
            // 先创建目标目录
            Directory.CreateDirectory(targetDir);

            // 先递归复制子目录
            foreach (string dir in Directory.GetDirectories(sourceDir))
            {
                string targetSubDir = Path.Combine(targetDir, Path.GetFileName(dir));
                CopyDirectory(dir, targetSubDir, overwrite);
            }

            // 再复制文件
            foreach (string file in Directory.GetFiles(sourceDir))
            {
                string targetFile = Path.Combine(targetDir, Path.GetFileName(file));
                File.Copy(file, targetFile, overwrite);
            }
        }


        public static void ChangeConfigTxt(string agentversion,string? service_basePath = null)
        {
            var parser = new FileIniDataParser();
            string localFilePath = Path.Combine(service_basePath, "settings.txt");

            IniData data = parser.ReadFile(localFilePath);
            data["kisCM"]["agentversion"] = agentversion;
            parser.WriteFile(localFilePath, data);
        }

        public static void SendDeviceInfo(string url, DateTime datetime, string ip, string pcname, string uname, string agentver, string token)
        {
            var DeviceInfo = new
            {
                registerdatetime = datetime,
                hostip = ip,
                hostname = pcname,
                username = uname,
                agentversion = agentver
            };

            var json = Newtonsoft.Json.JsonConvert.SerializeObject(DeviceInfo);

            System.Net.ServicePointManager.ServerCertificateValidationCallback +=
                (sender, cert, chain, sslPolicyErrors) => true;

            var request = (HttpWebRequest)WebRequest.Create(url);
            request.Method = "POST";
            request.ContentType = "application/json";
            request.Headers["Authorization"] = $"Bearer {token}";

            using (var streamWriter = new StreamWriter(request.GetRequestStream()))
            {
                streamWriter.Write(json);
                streamWriter.Flush();
                streamWriter.Close();
            }

            var response = (HttpWebResponse)request.GetResponse();
            using (var streamReader = new StreamReader(response.GetResponseStream()))
            {
                var result = streamReader.ReadToEnd();
                Logger.Instance.Log("服务器返回: " + result);
            }
        }

       

        /// <summary>
        /// 删除 updateagent_basePath 下一月前的备份目录
        /// </summary>
        public static void CleanOldBackups(string basePath)
        {
            try
            {
                if (!Directory.Exists(basePath)) return;

                var backupDirs = Directory.GetDirectories(basePath, "backup_*");

                foreach (var dir in backupDirs)
                {
                    // 获取目录名中的时间戳
                    string dirName = Path.GetFileName(dir);
                    string timestamp = dirName.Substring("backup_".Length);

                    if (DateTime.TryParseExact(timestamp, "yyyyMMddHHmmss", null,
                                               System.Globalization.DateTimeStyles.None,
                                               out DateTime backupTime))
                    {
                        // 如果备份超过30天，则删除
                        if ((DateTime.Now - backupTime).TotalDays > 30)
                        {
                            Directory.Delete(dir, true);
                            Logger.Instance.Log($"已删除旧备份目录: {dir}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Log($"清理旧备份失败: {ex.Message}");
            }
        }


    }
}
