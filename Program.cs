using System.Collections.Generic;
using System.Diagnostics;
using System.IO.Compression;
using System.Net;
using System.Security.AccessControl;
using System.Security.Cryptography.X509Certificates;
using IniParser;
using IniParser.Model;


namespace kisEM_CM_Agent_Update
{
    internal class Program
    {
        static void Main(string[] args)
        {
            /*下载的临时文件和备份都放到upateagent_basePath下
             
            1、停止服务 → systemctl stop kisem - cmagent

            2、备份目录 → 把服务可执行文件所在目录（及子目录）完整复制到一个备份目录
            下载并解压更新包 → 覆盖原目录

            3、启动服务 → systemctl start kisem - cmagent

            A、如果启动失败：

            恢复备份

            再次启动服务

            终止，不再执行 ChangeConfigTxt 和 SendDeviceInfo

            B、如果成功：继续执行 ChangeConfigTxt 和 SendDeviceInfo
            */
            Logger.Instance.Log("###开始进行agent升级......");
                      
            string updateagent_basePath = AppDomain.CurrentDomain.BaseDirectory;// 获取当前update程序所在目录

            string parentDir = Path.GetFullPath(Path.Combine(updateagent_basePath, ".."));
            Logger.Instance.Log($"parentDir: {parentDir}");
            string? service_basePath = Path.Combine(parentDir, "configMGMT"); ;// 获取服务所在目录，与update目录不同
                   

            string zipPath = Path.Combine(updateagent_basePath, "cmagent.zip");//下载后的zip文件
            string serviceName = "kisem-cmagent"; // Linux 下 systemd 服务名

            string hostname = Environment.MachineName;

            string downloadUrl = args[0];
            string vip = args[1];
            string localServerName = args[2];
            string localSSLPort = args[3];
            string agentversion = args[4];
            string hostip = args[5];
            string username = args[6];

            string devicebase_Request_URL = $"https://{localServerName}:{localSSLPort}/api/ReportDevice/{hostname}";

            try
            {

                // 1. 停止服务
                // 停止服务
                Logger.Instance.Log($"进入停止服务");

                // 先检查服务状态
                Logger.Instance.Log("检查服务当前状态...");
                ExecuteCommand($"systemctl is-active {serviceName}", 5000);

                // 停止服务
                Logger.Instance.Log("正在停止服务...");
                bool stopResult = ExecuteCommand($"systemctl stop {serviceName}", 30000);
                Logger.Instance.Log($"停止命令执行结果: {stopResult}");

                if (!stopResult)
                {
                    Logger.Instance.Log("正常停止失败，尝试强制停止...");
                    ExecuteCommand($"systemctl kill {serviceName}", 10000);
                    Logger.Instance.Log("等待2秒让进程完全停止...");
                    var endTime = DateTime.Now.AddSeconds(2);
                    while (DateTime.Now < endTime)
                    {
                        // 简单的忙等待
                    }
                    Logger.Instance.Log("强制停止等待完成");
                }
                else
                {
                    Logger.Instance.Log("服务停止成功，等待2秒确保完全停止...");
                    // 使用简单的循环等待，避免异步问题
                    var endTime = DateTime.Now.AddSeconds(2);
                    while (DateTime.Now < endTime)
                    {
                        // 简单的忙等待
                    }
                    Logger.Instance.Log("等待完成");
                }
                Logger.Instance.Log($"服务停止完成");

                /* 等待服务完全停止
                while (true)
                {
                    var check = new Process
                    {
                        StartInfo = new ProcessStartInfo
                        {
                            FileName = "/bin/bash",
                            Arguments = $"-c \"systemctl is-active --quiet {serviceName}\"",
                            UseShellExecute = false,
                            RedirectStandardOutput = false,
                            RedirectStandardError = false,
                            CreateNoWindow = true
                        }
                    };
                    check.Start();
                    check.WaitForExit();

                    if (check.ExitCode != 0) break; // 服务已停止
                    System.Threading.Thread.Sleep(1000); // 每秒检查一次
                }

                Logger.Instance.Log($"{serviceName} 已停止，继续执行更新程序");
                */
               


                // 2. 备份到updateagent_basePath
                Logger.Instance.Log($"进入备份");
                string backupDir = Path.Combine(updateagent_basePath, "backup_" + DateTime.Now.ToString("yyyyMMddHHmmss"));
                CopyDirectory(service_basePath, backupDir);
                Logger.Instance.Log($"已备份到: {backupDir}");

                // 3. 下载更新包
                if (File.Exists(zipPath)) File.Delete(zipPath);
                using (WebClient client = new WebClient())
                {
                    client.DownloadFile(downloadUrl, zipPath);
                }
                if (!File.Exists(zipPath))
                {
                    Logger.Instance.Log("下载失败，未找到 cmagent.zip");
                    return;
                }

                // 4. 解压覆盖
                ZipFile.ExtractToDirectory(zipPath, service_basePath, overwriteFiles: true);
                Logger.Instance.Log("解压完成！");

                // 5. 启动服务
                if (!ExecuteCommand($"systemctl start {serviceName}"))
                {
                    Logger.Instance.Log("新版本服务启动失败，正在恢复备份...");

                    // 恢复备份
                    CopyDirectory(backupDir, service_basePath, overwrite: true);

                    // 尝试启动旧版本服务
                    ExecuteCommand($"systemctl start {serviceName}");

                    Logger.Instance.Log("恢复完成，更新中止。");
                    return; // 不执行 ChangeConfigTxt 和 SendDeviceInfo
                }

                // 6. 修改配置
                ChangeConfigTxt(agentversion, service_basePath);

                // 7. 注册新版本
                SendDeviceInfo(devicebase_Request_URL, DateTime.Now, hostip, hostname, username, agentversion, vip);
                // 8. 清除一月前的备份
                CleanOldBackups(updateagent_basePath);

                Logger.Instance.Log("###更新完成！");
            }
            catch (Exception ex)
            {
                Logger.Instance.Log("###发生错误: " + ex.Message);
            }
        }

        // 执行 systemctl 命令




        // 执行命令（移除输出重定向以避免阻塞）
        public static bool ExecuteCommand(string command, int timeoutMs = 30000)
        {
            Logger.Instance.Log($"[DEBUG] 开始执行命令: {command}");

            try
            {
                using var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "/bin/bash",
                        Arguments = $"-c \"{command}\"",
                        UseShellExecute = false,  // 使用shell执行，让系统处理输出
                        CreateNoWindow = true,
                        WindowStyle = ProcessWindowStyle.Hidden
                    }
                };

                Logger.Instance.Log($"[DEBUG] 正在启动进程...");
                process.Start();
                Logger.Instance.Log($"[DEBUG] 进程已启动，PID: {process.Id}");

                Logger.Instance.Log($"[DEBUG] 等待进程结束，超时时间: {timeoutMs}ms");

                // 使用更细粒度的等待，每5秒输出一次状态
                int waitedMs = 0;
                const int checkIntervalMs = 5000;

                while (waitedMs < timeoutMs)
                {
                    if (process.WaitForExit(Math.Min(checkIntervalMs, timeoutMs - waitedMs)))
                    {
                        Logger.Instance.Log($"[DEBUG] 进程已结束，退出码: {process.ExitCode}");
                        Logger.Instance.Log($"命令执行完成: {command}, ExitCode: {process.ExitCode}");

                        // 对于 systemctl stop 命令，退出码 143 也是成功的（SIGTERM）
                        if (command.Contains("systemctl stop"))
                        {
                            bool success = process.ExitCode == 0 || process.ExitCode == 143;
                            Logger.Instance.Log($"systemctl stop 命令结果: {(success ? "成功" : "失败")}");
                            return success;
                        }

                        return process.ExitCode == 0;
                    }

                    waitedMs += checkIntervalMs;
                    Logger.Instance.Log($"[DEBUG] 进程仍在运行，已等待: {waitedMs}ms / {timeoutMs}ms");
                }

                // 超时处理
                Logger.Instance.Log($"[DEBUG] 进程超时，正在终止...");
                try
                {
                    if (!process.HasExited)
                    {
                        process.Kill();
                        process.WaitForExit(5000); // 等待5秒让进程完全终止
                    }
                }
                catch (Exception killEx)
                {
                    Logger.Instance.Log($"[DEBUG] 终止进程时出错: {killEx.Message}");
                }

                Logger.Instance.Log($"命令超时: {command}");
                return false;
            }
            catch (Exception ex)
            {
                Logger.Instance.Log($"执行命令异常: {command}, 异常: {ex.Message}");
                Logger.Instance.Log($"异常堆栈: {ex.StackTrace}");
                return false;
            }
        }



        // 递归复制目录
        public static void CopyDirectory(string sourceDir, string targetDir, bool overwrite = false)
        {
            // 先创建目标目录
            Directory.CreateDirectory(targetDir);

            // 先递归复制子目录
            foreach (string dir in Directory.GetDirectories(sourceDir))
            {
                string targetSubDir = Path.Combine(targetDir, Path.GetFileName(dir));
                CopyDirectory(dir, targetSubDir, overwrite);
            }

            // 再复制文件
            foreach (string file in Directory.GetFiles(sourceDir))
            {
                string targetFile = Path.Combine(targetDir, Path.GetFileName(file));
                File.Copy(file, targetFile, overwrite);
            }
        }


        public static void ChangeConfigTxt(string agentversion, string? service_basePath = null)
        {
            try
            {
                if (string.IsNullOrEmpty(service_basePath))
                {
                    Logger.Instance.Log("错误: service_basePath 参数为空");
                    return;
                }

                var parser = new FileIniDataParser();
                string localFilePath = Path.Combine(service_basePath, "settings.txt");

                if (!File.Exists(localFilePath))
                {
                    Logger.Instance.Log($"错误: 配置文件不存在: {localFilePath}");
                    return;
                }

                Logger.Instance.Log($"正在修改配置文件: {localFilePath}");
                IniData data = parser.ReadFile(localFilePath);
                data["kisCM"]["agentversion"] = agentversion;
                parser.WriteFile(localFilePath, data);
                Logger.Instance.Log($"配置文件修改成功，agentversion = {agentversion}");
            }
            catch (Exception ex)
            {
                Logger.Instance.Log($"修改配置文件失败: {ex.Message}");
            }
        }

        public static void SendDeviceInfo(string url, DateTime datetime, string ip, string pcname, string uname, string agentver, string token)
        {
            var DeviceInfo = new
            {
                registerdatetime = datetime,
                hostip = ip,
                hostname = pcname,
                username = uname,
                agentversion = agentver
            };

            var json = Newtonsoft.Json.JsonConvert.SerializeObject(DeviceInfo);

            System.Net.ServicePointManager.ServerCertificateValidationCallback +=
                (sender, cert, chain, sslPolicyErrors) => true;

            var request = (HttpWebRequest)WebRequest.Create(url);
            request.Method = "POST";
            request.ContentType = "application/json";
            request.Headers["Authorization"] = $"Bearer {token}";

            using (var streamWriter = new StreamWriter(request.GetRequestStream()))
            {
                streamWriter.Write(json);
                streamWriter.Flush();
                streamWriter.Close();
            }

            var response = (HttpWebResponse)request.GetResponse();
            using (var streamReader = new StreamReader(response.GetResponseStream()))
            {
                var result = streamReader.ReadToEnd();
                Logger.Instance.Log("服务器返回: " + result);
            }
        }

       

        /// <summary>
        /// 删除 updateagent_basePath 下一月前的备份目录
        /// </summary>
        public static void CleanOldBackups(string basePath)
        {
            try
            {
                if (!Directory.Exists(basePath)) return;

                var backupDirs = Directory.GetDirectories(basePath, "backup_*");

                foreach (var dir in backupDirs)
                {
                    // 获取目录名中的时间戳
                    string dirName = Path.GetFileName(dir);
                    string timestamp = dirName.Substring("backup_".Length);

                    if (DateTime.TryParseExact(timestamp, "yyyyMMddHHmmss", null,
                                               System.Globalization.DateTimeStyles.None,
                                               out DateTime backupTime))
                    {
                        // 如果备份超过30天，则删除
                        if ((DateTime.Now - backupTime).TotalDays > 30)
                        {
                            Directory.Delete(dir, true);
                            Logger.Instance.Log($"已删除旧备份目录: {dir}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Log($"清理旧备份失败: {ex.Message}");
            }
        }


    }
}
