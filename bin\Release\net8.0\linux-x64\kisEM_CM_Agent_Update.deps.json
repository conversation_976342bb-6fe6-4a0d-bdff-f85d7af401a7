{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0/linux-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {}, ".NETCoreApp,Version=v8.0/linux-x64": {"kisEM_CM_Agent_Update/1.0.0": {"dependencies": {"Newtonsoft.Json": "13.0.3", "ini-parser": "2.5.2", "runtimepack.Microsoft.NETCore.App.Runtime.linux-x64": "8.0.3"}, "runtime": {"kisEM_CM_Agent_Update.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.linux-x64/8.0.3": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "********", "fileVersion": "13.0.324.11423"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "8.0.324.11423"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Formats.Tar.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Net.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Runtime.InteropServices.JavaScript.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Security.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "netstandard.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "8.0.324.11423"}}, "native": {"createdump": {"fileVersion": "0.0.0.0"}, "libSystem.Globalization.Native.so": {"fileVersion": "0.0.0.0"}, "libSystem.IO.Compression.Native.so": {"fileVersion": "0.0.0.0"}, "libSystem.Native.so": {"fileVersion": "0.0.0.0"}, "libSystem.Net.Security.Native.so": {"fileVersion": "0.0.0.0"}, "libSystem.Security.Cryptography.Native.OpenSsl.so": {"fileVersion": "0.0.0.0"}, "libclrgc.so": {"fileVersion": "0.0.0.0"}, "libclrjit.so": {"fileVersion": "0.0.0.0"}, "libcoreclr.so": {"fileVersion": "0.0.0.0"}, "libcoreclrtraceptprovider.so": {"fileVersion": "0.0.0.0"}, "libhostfxr.so": {"fileVersion": "0.0.0.0"}, "libhostpolicy.so": {"fileVersion": "0.0.0.0"}, "libmscordaccore.so": {"fileVersion": "0.0.0.0"}, "libmscordbi.so": {"fileVersion": "0.0.0.0"}}}, "ini-parser/2.5.2": {"runtime": {"lib/net20/INIFileParser.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}}}, "libraries": {"kisEM_CM_Agent_Update/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.linux-x64/8.0.3": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "ini-parser/2.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-hp3gKmC/14+6eKLgv7Jd1Z7OV86lO+tNfOXr/stQbwmRhdQuXVSvrRAuAe7G5+lwhkov0XkqZ8/bn1PYWMx6eg==", "path": "ini-parser/2.5.2", "hashPath": "ini-parser.2.5.2.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}}, "runtimes": {"android-x64": ["android", "linux-bionic-x64", "linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linux-bionic-x64": ["linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linux-musl-x64": ["linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linux-x64": ["linux", "unix-x64", "unix", "any", "base"]}}