{"format": 1, "restore": {"D:\\VB2\\kisEM_CM_Agent_Update\\kisEM_CM_Agent_Update.csproj": {}}, "projects": {"D:\\VB2\\kisEM_CM_Agent_Update\\kisEM_CM_Agent_Update.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\VB2\\kisEM_CM_Agent_Update\\kisEM_CM_Agent_Update.csproj", "projectName": "kisEM_CM_Agent_Update", "projectPath": "D:\\VB2\\kisEM_CM_Agent_Update\\kisEM_CM_Agent_Update.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\VB2\\kisEM_CM_Agent_Update\\obj\\publish\\linux-x64\\", "projectStyle": "PackageReference", "fallbackFolders": ["d:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[8.0.3, )", "autoReferenced": true}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "ini-parser": {"target": "Package", "version": "[2.5.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.linux-x64", "version": "[8.0.3, 8.0.3]"}, {"name": "Microsoft.NETCore.App.Host.linux-x64", "version": "[8.0.3, 8.0.3]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-x64", "version": "[8.0.3, 8.0.3]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.202/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"linux-x64": {"#import": []}}}}}