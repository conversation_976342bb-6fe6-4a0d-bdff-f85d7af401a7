﻿using System;
using System.IO;
using System.Reflection;

namespace kisEM_CM_Agent_Update
{
    //调用：Logger.Instance.Log("This is a log message from SomeClass.");
    public class Logger
    {
        private static readonly object _lock = new object(); // 确保多线程安全
        private static Logger _instance;
        private static readonly string _logFilePath;

        // 静态构造函数，初始化日志文件路径
        static Logger()
        {
            // 获取当前可执行程序所在目录
            string exeDirectory = AppDomain.CurrentDomain.BaseDirectory;
            // 设置日志文件路径为程序同目录下的 log.txt
            _logFilePath = Path.Combine(exeDirectory, "updatelog.txt");
        }

        // 私有构造函数，单例模式
        private Logger()
        {
            // 创建日志目录（如果不存在）
            string logDirectory = Path.GetDirectoryName(_logFilePath);
            if (!Directory.Exists(logDirectory))
            {
                Directory.CreateDirectory(logDirectory);
            }
        }

        // 获取单例实例
        public static Logger Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new Logger();
                }
                return _instance;
            }
        }

        // 写日志的方法
        public void Log(string message)
        {
            lock (_lock) // 确保线程安全
            {
                try
                {
                    // 检查日志文件大小，超过10MB就轮转
                    if (File.Exists(_logFilePath) && new FileInfo(_logFilePath).Length > 10 * 1024 * 1024)
                    {
                        string backupPath = _logFilePath + ".old";
                        if (File.Exists(backupPath)) File.Delete(backupPath);
                        File.Move(_logFilePath, backupPath);
                    }

                    using (StreamWriter writer = new StreamWriter(_logFilePath, true))
                    {
                        string logMessage = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}";
                        writer.WriteLine(logMessage);
                    }
                }
                catch
                {
                    // 忽略日志写入错误，避免影响主程序
                }
            }
        }
    }
}