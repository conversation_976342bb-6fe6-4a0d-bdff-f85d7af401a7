{"version": 2, "dgSpecHash": "nktSqkYkpUxcORc5JGVJ6J4grM+UJqYz1dKOavHysyyji5JyMqTDHvGRyKi1U9PBxy7bXDC2D+D0aBCqPDDT2A==", "success": true, "projectFilePath": "D:\\VB2\\kisEM_CM_Agent_Update\\kisEM_CM_Agent_Update.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\ini-parser\\2.5.2\\ini-parser.2.5.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.app.runtime.linux-x64\\8.0.3\\microsoft.netcore.app.runtime.linux-x64.8.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.app.runtime.linux-x64\\8.0.3\\microsoft.aspnetcore.app.runtime.linux-x64.8.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.app.host.linux-x64\\8.0.3\\microsoft.netcore.app.host.linux-x64.8.0.3.nupkg.sha512"], "logs": [{"code": "NU1701", "level": "Warning", "warningLevel": 1, "message": "已使用“.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1”而不是项目目标框架“net8.0”还原包“ini-parser 2.5.2”。此包可能与项目不完全兼容。", "libraryId": "ini-parser", "targetGraphs": ["net8.0"]}]}